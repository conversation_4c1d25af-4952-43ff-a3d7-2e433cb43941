import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"

import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Input,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useState } from "react"
import { FaPlus } from "react-icons/fa"

import { type FloorCreate, FloorsService } from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
  DialogTrigger,
} from "../ui/dialog"
import { Field } from "../ui/field"

interface AddFloorProps {
  buildingId: string
}

const AddFloor = ({ buildingId }: AddFloorProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid, isSubmitting },
  } = useForm<FloorCreate>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: {
      number: 0,
      building_id: buildingId,
      floor_plan_image: "",
    },
  })

  const mutation = useMutation({
    mutationFn: (data: FloorCreate) =>
      FloorsService.createFloor({ requestBody: data }),
    onSuccess: () => {
      showSuccessToast("Floor created successfully.")
      reset()
      setIsOpen(false)
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["floors"] })
    },
  })

  const onSubmit: SubmitHandler<FloorCreate> = (data) => {
    mutation.mutate(data)
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => setIsOpen(open)}
    >
      <DialogTrigger asChild>
        <Button value="add-floor" my={4}>
          <FaPlus fontSize="16px" />
          Add Floor
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Add Floor</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <Text mb={4}>Fill in the details to add a new floor.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.number}
                errorText={errors.number?.message}
                label="Floor Number"
              >
                <Input
                  id="number"
                  {...register("number", {
                    required: "Floor number is required.",
                    valueAsNumber: true,
                    min: {
                      value: 0,
                      message: "Floor number must be 0 or greater",
                    },
                  })}
                  placeholder="0"
                  type="number"
                />
              </Field>

              <Field
                invalid={!!errors.floor_plan_image}
                errorText={errors.floor_plan_image?.message}
                label="Floor Plan Image URL"
              >
                <Input
                  id="floor_plan_image"
                  {...register("floor_plan_image")}
                  placeholder="https://example.com/floor-plan.jpg"
                  type="text"
                />
              </Field>
            </VStack>
          </DialogBody>

          <DialogFooter gap={2}>
            <DialogActionTrigger asChild>
              <Button
                variant="subtle"
                colorPalette="gray"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              disabled={!isValid}
              loading={isSubmitting}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default AddFloor
