import { useMutation, useQueryClient } from "@tanstack/react-query"
import { type SubmitHandler, useForm } from "react-hook-form"

import {
  Button,
  DialogActionTrigger,
  DialogTitle,
  Input,
  Text,
  VStack,
} from "@chakra-ui/react"
import { useEffect } from "react"

import {
  type FloorPublic,
  type FloorUpdate,
  FloorsService,
} from "@/client"
import type { ApiError } from "@/client/core/ApiError"
import useCustomToast from "@/hooks/useCustomToast"
import { handleError } from "@/utils"
import {
  DialogBody,
  DialogCloseTrigger,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogRoot,
} from "../ui/dialog"
import { Field } from "../ui/field"

interface EditFloorProps {
  floor: FloorPublic
  isOpen: boolean
  onClose: () => void
}

const EditFloor = ({ floor, isOpen, onClose }: EditFloorProps) => {
  const queryClient = useQueryClient()
  const { showSuccessToast } = useCustomToast()
  const {
    register,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, isDirty },
  } = useForm<FloorUpdate>({
    mode: "onBlur",
    criteriaMode: "all",
    defaultValues: floor,
  })

  useEffect(() => {
    reset(floor)
  }, [floor, reset])

  const mutation = useMutation({
    mutationFn: (data: FloorUpdate) =>
      FloorsService.updateFloor({
        floorId: floor.id,
        requestBody: data,
      }),
    onSuccess: () => {
      showSuccessToast("Floor updated successfully.")
      onClose()
    },
    onError: (err: ApiError) => {
      handleError(err)
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["floors"] })
    },
  })

  const onSubmit: SubmitHandler<FloorUpdate> = async (data) => {
    mutation.mutate(data)
  }

  const onCancel = () => {
    reset()
    onClose()
  }

  return (
    <DialogRoot
      size={{ base: "xs", md: "md" }}
      placement="center"
      open={isOpen}
      onOpenChange={({ open }) => !open && onClose()}
    >
      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Edit Floor</DialogTitle>
          </DialogHeader>

          <DialogBody>
            <Text mb={4}>Update the floor details.</Text>
            <VStack gap={4}>
              <Field
                required
                invalid={!!errors.number}
                errorText={errors.number?.message}
                label="Floor Number"
              >
                <Input
                  id="number"
                  {...register("number", {
                    required: "Floor number is required",
                    valueAsNumber: true,
                    min: {
                      value: 0,
                      message: "Floor number must be 0 or greater",
                    },
                  })}
                  placeholder="0"
                  type="number"
                />
              </Field>

              <Field
                invalid={!!errors.floor_plan_image}
                errorText={errors.floor_plan_image?.message}
                label="Floor Plan Image URL"
              >
                <Input
                  id="floor_plan_image"
                  {...register("floor_plan_image")}
                  placeholder="https://example.com/floor-plan.jpg"
                  type="text"
                />
              </Field>
            </VStack>
          </DialogBody>

          <DialogFooter gap={2}>
            <DialogActionTrigger asChild>
              <Button
                variant="subtle"
                colorPalette="gray"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogActionTrigger>
            <Button
              variant="solid"
              type="submit"
              disabled={isSubmitting || !isDirty}
              loading={isSubmitting}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
        <DialogCloseTrigger />
      </DialogContent>
    </DialogRoot>
  )
}

export default EditFloor
