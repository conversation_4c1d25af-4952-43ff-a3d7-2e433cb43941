import os
import uuid
from pathlib import Path
from typing import Optional
import boto3
from botocore.exceptions import ClientError
from fastapi import UploadFile
from app.core.config import settings


class FileUploadService:
    def __init__(self):
        self.cloud_environment = os.getenv("CLOUD_ENVIRONMENT", "local")
        self.media_path = Path("media")
        
        if self.cloud_environment == "aws":
            self.s3_client = boto3.client(
                "s3",
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "us-east-1")
            )
            self.s3_bucket = os.getenv("AWS_S3_BUCKET", "school-floor-plans")
    
    async def upload_floor_plan(self, file: UploadFile, floor_id: str) -> Optional[str]:
        """Upload floor plan image to local storage or S3 based on environment"""
        if not file:
            return None
        
        # Generate unique filename
        file_extension = file.filename.split(".")[-1] if "." in file.filename else "jpg"
        unique_filename = f"floor_plans/{floor_id}_{uuid.uuid4()}.{file_extension}"
        
        if self.cloud_environment == "aws":
            return await self._upload_to_s3(file, unique_filename)
        else:
            return await self._upload_to_local(file, unique_filename)
    
    async def _upload_to_local(self, file: UploadFile, filename: str) -> str:
        """Upload file to local media directory"""
        # Create media directory if it doesn't exist
        file_path = self.media_path / filename
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save file
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Return relative path
        return f"/media/{filename}"
    
    async def _upload_to_s3(self, file: UploadFile, filename: str) -> str:
        """Upload file to AWS S3"""
        try:
            content = await file.read()
            self.s3_client.put_object(
                Bucket=self.s3_bucket,
                Key=filename,
                Body=content,
                ContentType=file.content_type or "image/jpeg"
            )
            
            # Return S3 URL
            return f"https://{self.s3_bucket}.s3.amazonaws.com/{filename}"
        except ClientError as e:
            print(f"Error uploading to S3: {e}")
            raise Exception("Failed to upload file to S3")
    
    async def delete_floor_plan(self, file_url: str) -> bool:
        """Delete floor plan image from storage"""
        if not file_url:
            return True
        
        if self.cloud_environment == "aws":
            return await self._delete_from_s3(file_url)
        else:
            return await self._delete_from_local(file_url)
    
    async def _delete_from_local(self, file_url: str) -> bool:
        """Delete file from local storage"""
        try:
            # Extract relative path from URL
            relative_path = file_url.replace("/media/", "")
            file_path = self.media_path / relative_path
            
            if file_path.exists():
                file_path.unlink()
            return True
        except Exception as e:
            print(f"Error deleting local file: {e}")
            return False
    
    async def _delete_from_s3(self, file_url: str) -> bool:
        """Delete file from S3"""
        try:
            # Extract key from S3 URL
            key = file_url.split(f"{self.s3_bucket}.s3.amazonaws.com/")[-1]
            self.s3_client.delete_object(Bucket=self.s3_bucket, Key=key)
            return True
        except ClientError as e:
            print(f"Error deleting from S3: {e}")
            return False


# Singleton instance
file_upload_service = FileUploadService()
