import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models import Building, Floor, School
from app.tests.utils.school import create_random_school
from app.tests.utils.utils import random_lower_string


def create_random_building(db: Session, school_id: str) -> Building:
    building = Building(
        name=random_lower_string(),
        school_id=school_id,
    )
    db.add(building)
    db.commit()
    db.refresh(building)
    return building


def test_create_floor(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = create_random_building(db, school.id)
    data = {
        "number": 1,
        "building_id": str(building.id),
        "floor_plan_image": "https://example.com/floor1.jpg",
    }
    response = client.post(
        f"{settings.API_V1_STR}/floors/",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["number"] == data["number"]
    assert content["building_id"] == data["building_id"]
    assert content["floor_plan_image"] == data["floor_plan_image"]
    assert "id" in content
    assert "created_at" in content


def test_read_floor(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = create_random_building(db, school.id)
    floor = Floor(
        number=1,
        building_id=building.id,
        floor_plan_image="https://example.com/floor1.jpg",
    )
    db.add(floor)
    db.commit()
    db.refresh(floor)

    response = client.get(
        f"{settings.API_V1_STR}/floors/{floor.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["number"] == floor.number
    assert content["building_id"] == str(floor.building_id)
    assert content["id"] == str(floor.id)


def test_read_floors(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = create_random_building(db, school.id)
    # Create multiple floors
    for i in range(3):
        floor = Floor(
            number=i,
            building_id=building.id,
        )
        db.add(floor)
    db.commit()

    response = client.get(
        f"{settings.API_V1_STR}/floors/",
        headers=superuser_token_headers,
        params={"building_id": str(building.id)},
    )
    assert response.status_code == 200
    content = response.json()
    assert len(content["data"]) >= 3
    assert content["count"] >= 3


def test_update_floor(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = create_random_building(db, school.id)
    floor = Floor(
        number=1,
        building_id=building.id,
        floor_plan_image="https://example.com/floor1.jpg",
    )
    db.add(floor)
    db.commit()
    db.refresh(floor)

    data = {
        "number": 2,
        "floor_plan_image": "https://example.com/floor2-updated.jpg",
    }
    response = client.patch(
        f"{settings.API_V1_STR}/floors/{floor.id}",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["number"] == data["number"]
    assert content["floor_plan_image"] == data["floor_plan_image"]


def test_delete_floor(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = create_random_building(db, school.id)
    floor = Floor(
        number=1,
        building_id=building.id,
    )
    db.add(floor)
    db.commit()
    db.refresh(floor)

    response = client.delete(
        f"{settings.API_V1_STR}/floors/{floor.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200
    content = response.json()
    assert content["message"] == "Floor deleted successfully"

    # Verify soft delete
    db.refresh(floor)
    assert floor.deleted_at is not None


def test_create_floor_not_found_building(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    data = {
        "number": 1,
        "building_id": "00000000-0000-0000-0000-000000000000",
    }
    response = client.post(
        f"{settings.API_V1_STR}/floors/",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 404


def test_read_floor_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    response = client.get(
        f"{settings.API_V1_STR}/floors/00000000-0000-0000-0000-000000000000",
        headers=superuser_token_headers,
    )
    assert response.status_code == 404


def test_update_floor_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    data = {"number": 2}
    response = client.patch(
        f"{settings.API_V1_STR}/floors/00000000-0000-0000-0000-000000000000",
        headers=superuser_token_headers,
        json=data,
    )
    assert response.status_code == 404


def test_delete_floor_not_found(
    client: TestClient, superuser_token_headers: dict[str, str]
) -> None:
    response = client.delete(
        f"{settings.API_V1_STR}/floors/00000000-0000-0000-0000-000000000000",
        headers=superuser_token_headers,
    )
    assert response.status_code == 404


def test_cascade_delete_building_deletes_floors(
    client: TestClient, superuser_token_headers: dict[str, str], db: Session
) -> None:
    school = create_random_school(db)
    building = create_random_building(db, school.id)
    
    # Create multiple floors
    floor_ids = []
    for i in range(3):
        floor = Floor(
            number=i,
            building_id=building.id,
        )
        db.add(floor)
        db.commit()
        db.refresh(floor)
        floor_ids.append(floor.id)

    # Delete the building
    response = client.delete(
        f"{settings.API_V1_STR}/buildings/{building.id}",
        headers=superuser_token_headers,
    )
    assert response.status_code == 200

    # Verify all floors are soft deleted
    for floor_id in floor_ids:
        floor = db.query(Floor).filter(Floor.id == floor_id).first()
        assert floor.deleted_at is not None
