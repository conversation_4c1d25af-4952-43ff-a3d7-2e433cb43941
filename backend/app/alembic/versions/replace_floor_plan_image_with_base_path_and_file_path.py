"""replace floor_plan_image with base_path and file_path

Revision ID: replace_floor_plan_image
Revises: add_soft_delete_001
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'replace_floor_plan_image'
down_revision = 'add_soft_delete_001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove the old floor_plan_image column
    op.drop_column('floor', 'floor_plan_image')
    
    # Add the new base_path and file_path columns
    op.add_column('floor', sa.Column('base_path', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    op.add_column('floor', sa.Column('file_path', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove the new columns
    op.drop_column('floor', 'file_path')
    op.drop_column('floor', 'base_path')
    
    # Add back the old floor_plan_image column
    op.add_column('floor', sa.Column('floor_plan_image', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    # ### end Alembic commands ###
