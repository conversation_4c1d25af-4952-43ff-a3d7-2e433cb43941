import uuid
from datetime import datetime
from enum import Enum

from pydantic import EmailStr
from sqlmodel import Field, Relationship, SQLModel


# RBAC Enums and Models
class RoleType(str, Enum):
    ADMIN = "admin"
    TEACHERS = "teachers"
    MANAGEMENT = "management"
    SECURITY = "security"
    IT = "it"


class PermissionType(str, Enum):
    # User management permissions
    CREATE_USER = "create_user"
    READ_USER = "read_user"
    UPDATE_USER = "update_user"
    DELETE_USER = "delete_user"

    # School management permissions
    CREATE_SCHOOL = "create_school"
    READ_SCHOOL = "read_school"
    UPDATE_SCHOOL = "update_school"
    DELETE_SCHOOL = "delete_school"

    # Threat detection permissions
    VIEW_THREATS = "view_threats"
    MANAGE_THREATS = "manage_threats"
    CONFIGURE_CAMERAS = "configure_cameras"

    # System permissions
    SYSTEM_CONFIG = "system_config"
    VIEW_LOGS = "view_logs"
    MANAGE_ROLES = "manage_roles"

    # Academic permissions
    MANAGE_CLASSES = "manage_classes"
    VIEW_STUDENTS = "view_students"
    MANAGE_CURRICULUM = "manage_curriculum"


# Role model
class RoleBase(SQLModel):
    name: RoleType = Field(unique=True, index=True)
    description: str | None = Field(default=None, max_length=255)
    is_active: bool = True


class RoleCreate(RoleBase):
    pass


class RoleUpdate(SQLModel):
    description: str | None = Field(default=None, max_length=255)
    is_active: bool | None = None


class Role(RoleBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime | None = None

    # Relationships
    user_roles: list["UserRole"] = Relationship(back_populates="role", cascade_delete=True)
    role_permissions: list["RolePermission"] = Relationship(back_populates="role", cascade_delete=True)


class RolePublic(RoleBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime | None = None


class RolesPublic(SQLModel):
    data: list[RolePublic]
    count: int


# Permission model
class PermissionBase(SQLModel):
    name: PermissionType = Field(unique=True, index=True)
    description: str | None = Field(default=None, max_length=255)
    resource: str | None = Field(default=None, max_length=100)  # e.g., "user", "school", "threat"


class PermissionCreate(PermissionBase):
    pass


class PermissionUpdate(SQLModel):
    description: str | None = Field(default=None, max_length=255)
    resource: str | None = Field(default=None, max_length=100)


class Permission(PermissionBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    role_permissions: list["RolePermission"] = Relationship(back_populates="permission", cascade_delete=True)


class PermissionPublic(PermissionBase):
    id: uuid.UUID
    created_at: datetime


class PermissionsPublic(SQLModel):
    data: list[PermissionPublic]
    count: int


# Association tables
class UserRole(SQLModel, table=True):
    user_id: uuid.UUID = Field(foreign_key="user.id", primary_key=True, ondelete="CASCADE")
    role_id: uuid.UUID = Field(foreign_key="role.id", primary_key=True, ondelete="CASCADE")
    assigned_at: datetime = Field(default_factory=datetime.utcnow)
    assigned_by: uuid.UUID | None = Field(foreign_key="user.id", default=None)

    # Relationships
    user: "User" = Relationship(
        back_populates="user_roles",
        sa_relationship_kwargs={"foreign_keys": "[UserRole.user_id]"}
    )
    role: Role = Relationship(back_populates="user_roles")


class RolePermission(SQLModel, table=True):
    role_id: uuid.UUID = Field(foreign_key="role.id", primary_key=True, ondelete="CASCADE")
    permission_id: uuid.UUID = Field(foreign_key="permission.id", primary_key=True, ondelete="CASCADE")
    granted_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    role: Role = Relationship(back_populates="role_permissions")
    permission: Permission = Relationship(back_populates="role_permissions")


# User role assignment models
class UserRoleAssign(SQLModel):
    user_id: uuid.UUID
    role_id: uuid.UUID


class UserRoleRemove(SQLModel):
    user_id: uuid.UUID
    role_id: uuid.UUID


class RolePermissionAssign(SQLModel):
    role_id: uuid.UUID
    permission_id: uuid.UUID


class RolePermissionRemove(SQLModel):
    role_id: uuid.UUID
    permission_id: uuid.UUID


# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)
    user_roles: list[UserRole] = Relationship(
        back_populates="user",
        cascade_delete=True,
        sa_relationship_kwargs={"foreign_keys": "[UserRole.user_id]"}
    )


# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: uuid.UUID
    roles: list[RolePublic] = []


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


# School models
class SchoolBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    address: str | None = Field(default=None, max_length=255)
    phone: str | None = Field(default=None, max_length=50)
    email: str | None = Field(default=None, max_length=255)


# Properties to receive on school creation
class SchoolCreate(SchoolBase):
    pass


# Properties to receive on school update
class SchoolUpdate(SchoolBase):
    name: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model
class School(SchoolBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime | None = None
    is_deleted: bool = Field(default=False, index=True)
    deleted_at: datetime | None = None

    # Relationships
    buildings: list["Building"] = Relationship(back_populates="school", sa_relationship_kwargs={"cascade": "all, delete-orphan"})


# Building models
class BuildingBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    school_id: uuid.UUID = Field(foreign_key="school.id")
    address: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=500)


class BuildingCreate(BuildingBase):
    pass


class BuildingUpdate(BuildingBase):
    name: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore
    school_id: uuid.UUID | None = None  # type: ignore
    address: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=500)


class Building(BuildingBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime | None = None
    is_deleted: bool = Field(default=False, index=True)
    deleted_at: datetime | None = None

    # Relationships
    school: School = Relationship(back_populates="buildings")
    floors: list["Floor"] = Relationship(back_populates="building", sa_relationship_kwargs={"cascade": "all, delete-orphan"})


class BuildingPublic(BuildingBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime | None = None


class BuildingsPublic(SQLModel):
    data: list[BuildingPublic]
    count: int


# Floor models
class FloorBase(SQLModel):
    number: int = Field(ge=0)
    building_id: uuid.UUID = Field(foreign_key="building.id")
    floor_plan_image: str | None = Field(default=None, max_length=500)


class FloorCreate(FloorBase):
    pass


class FloorUpdate(SQLModel):
    number: int | None = Field(default=None, ge=0)  # type: ignore
    floor_plan_image: str | None = Field(default=None, max_length=500)


class Floor(FloorBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime | None = None
    is_deleted: bool = Field(default=False, index=True)
    deleted_at: datetime | None = None

    # Relationships
    building: Building = Relationship(back_populates="floors")
    cameras: list["Camera"] = Relationship(back_populates="floor", sa_relationship_kwargs={"cascade": "all, delete-orphan"})


class FloorPublic(FloorBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime | None = None


class FloorsPublic(SQLModel):
    data: list[FloorPublic]
    count: int


# Camera models
class CameraBase(SQLModel):
    position: str = Field(min_length=1, max_length=255)
    floor_id: uuid.UUID = Field(foreign_key="floor.id")


class CameraCreate(CameraBase):
    pass


class CameraUpdate(SQLModel):
    position: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


class Camera(CameraBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime | None = None
    is_deleted: bool = Field(default=False, index=True)
    deleted_at: datetime | None = None

    # Relationships
    floor: Floor = Relationship(back_populates="cameras")


class CameraPublic(CameraBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime | None = None


class CamerasPublic(SQLModel):
    data: list[CameraPublic]
    count: int

# Properties to return via API
class SchoolPublic(SchoolBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime | None = None


class SchoolsPublic(SQLModel):
    data: list[SchoolPublic]
    count: int


# Threat Detection Models
class ThreatDetectionBase(SQLModel):
    camera_id: str = Field(max_length=255)
    threat_type: str = Field(max_length=100)
    confidence: float = Field(ge=0.0, le=1.0)
    bbox_x1: int
    bbox_y1: int
    bbox_x2: int
    bbox_y2: int
    image_path: str | None = Field(default=None, max_length=500)
    is_active_threat: bool = True


# Properties to receive on threat detection creation
class ThreatDetectionCreate(ThreatDetectionBase):
    pass


# Properties to receive on threat detection update
class ThreatDetectionUpdate(SQLModel):
    is_active_threat: bool | None = None


# Database model
class ThreatDetection(ThreatDetectionBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    detected_at: datetime = Field(default_factory=datetime.utcnow)
    resolved_at: datetime | None = None


# Properties to return via API
class ThreatDetectionPublic(ThreatDetectionBase):
    id: uuid.UUID
    detected_at: datetime
    resolved_at: datetime | None = None


class ThreatDetectionsPublic(SQLModel):
    data: list[ThreatDetectionPublic]
    count: int


# WebSocket message models
class DetectionFrame(SQLModel):
    frame: str  # base64 encoded image
    detections: list[dict]
    timestamp: float
    threats_detected: bool = False


class ThreatAlert(SQLModel):
    threat_id: uuid.UUID
    threat_type: str
    confidence: float
    camera_id: str
    timestamp: datetime
    bbox: list[int]  # [x1, y1, x2, y2]
    message: str
